"use client"

import { memo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertTriangle } from "lucide-react"
import type { BusinessSettingsFormData } from "@/hooks/use-business-settings-form"

interface DeliveryTabProps {
  formData: BusinessSettingsFormData
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  handleSelectChange: (name: string, value: string) => void
  handleSwitchChange: (name: string, checked: boolean) => void
}

// Validation function to ensure at least one service is enabled
const validateServiceOptions = (deliveryAvailable: boolean, pickupAvailable: boolean): boolean => {
  return deliveryAvailable || pickupAvailable
}

export const DeliveryTab = memo(function DeliveryTab({
  formData,
  handleChange,
  handleSelectChange,
  handleSwitchChange
}: DeliveryTabProps) {
  const isValidServiceSelection = validateServiceOptions(formData.delivery_available, formData.pickup_available)

  // Enhanced switch handler with validation
  const handleServiceSwitchChange = (name: string, checked: boolean) => {
    // Prevent disabling both services
    if (!checked) {
      if (name === 'delivery_available' && !formData.pickup_available) {
        return // Don't allow disabling delivery if pickup is already disabled
      }
      if (name === 'pickup_available' && !formData.delivery_available) {
        return // Don't allow disabling pickup if delivery is already disabled
      }
    }
    handleSwitchChange(name, checked)
  }

  return (
    <div className="space-y-6">
      {/* Validation Alert */}
      {!isValidServiceSelection && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Your business must offer at least one service option (delivery or pickup).
          </AlertDescription>
        </Alert>
      )}

      {/* Service Options Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Service Options</CardTitle>
          <CardDescription>Choose which services your business offers to customers</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Delivery Toggle */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">Delivery Service</Label>
                  <p className="text-xs text-gray-600">Deliver orders to customers</p>
                </div>
                <Switch
                  checked={formData.delivery_available}
                  onCheckedChange={(checked) => handleServiceSwitchChange('delivery_available', checked)}
                />
              </div>
            </div>

            {/* Pickup Toggle */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">Pickup Service</Label>
                  <p className="text-xs text-gray-600">Customers collect orders</p>
                </div>
                <Switch
                  checked={formData.pickup_available}
                  onCheckedChange={(checked) => handleServiceSwitchChange('pickup_available', checked)}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delivery Settings */}
      {formData.delivery_available && (
        <Card>
          <CardHeader>
            <CardTitle>Delivery Settings</CardTitle>
            <CardDescription>Configure your delivery options and pricing</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="delivery_radius">Delivery Radius (km)</Label>
                  <Input
                    id="delivery_radius"
                    name="delivery_radius"
                    type="number"
                    min="0"
                    step="0.5"
                    value={formData.delivery_radius}
                    onChange={handleChange}
                    placeholder="e.g., 5"
                  />
                </div>
                <div>
                  <Label htmlFor="delivery_fee">Delivery Fee (£)</Label>
                  <Input
                    id="delivery_fee"
                    name="delivery_fee"
                    type="number"
                    min="0"
                    step="0.25"
                    value={formData.delivery_fee}
                    onChange={handleChange}
                    placeholder="e.g., 2.50"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pickup Settings */}
      {formData.pickup_available && (
        <Card>
          <CardHeader>
            <CardTitle>Pickup Settings</CardTitle>
            <CardDescription>Configure pickup options for your business</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                <p>Pickup settings can be configured here. Currently, pickup is enabled and customers can collect orders from your business location.</p>
              </div>
              {/* Future pickup-specific settings can be added here */}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
})
