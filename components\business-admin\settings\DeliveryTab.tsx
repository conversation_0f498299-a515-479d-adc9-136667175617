"use client"

import { memo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import type { BusinessSettingsFormData } from "@/hooks/use-business-settings-form"

interface DeliveryTabProps {
  formData: BusinessSettingsFormData
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  handleSelectChange: (name: string, value: string) => void
  handleSwitchChange: (name: string, checked: boolean) => void
}

export const DeliveryTab = memo(function DeliveryTab({
  formData,
  handleChange,
  handleSelectChange,
  handleSwitchChange
}: DeliveryTabProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Delivery Settings</CardTitle>
          <CardDescription>Configure your delivery options and settings</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Delivery Available</Label>
                <p className="text-xs text-gray-600">Enable delivery for your business</p>
              </div>
              <Switch
                checked={formData.delivery_available}
                onCheckedChange={(checked) => handleSwitchChange('delivery_available', checked)}
              />
            </div>

            {formData.delivery_available && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="delivery_radius">Delivery Radius (km)</Label>
                    <Input
                      id="delivery_radius"
                      name="delivery_radius"
                      type="number"
                      value={formData.delivery_radius}
                      onChange={handleChange}
                    />
                  </div>
                  <div>
                    <Label htmlFor="delivery_fee">Delivery Fee (£)</Label>
                    <Input
                      id="delivery_fee"
                      name="delivery_fee"
                      type="number"
                      step="0.25"
                      value={formData.delivery_fee}
                      onChange={handleChange}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Pickup Options</CardTitle>
          <CardDescription>Configure pickup settings for your business</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Pickup Available</Label>
                <p className="text-xs text-gray-600">Allow customers to collect orders</p>
              </div>
              <Switch
                checked={formData.pickup_available}
                onCheckedChange={(checked) => handleSwitchChange('pickup_available', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
})
