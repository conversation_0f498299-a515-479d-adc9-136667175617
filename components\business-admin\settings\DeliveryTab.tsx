"use client"

import { memo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Toolt<PERSON>, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"
import { Separator } from "@/components/ui/separator"
import { Truck, HelpCircle, CheckCircle, AlertCircle, DollarSign, Clock, MapPin } from "lucide-react"
import type { BusinessSettingsFormData } from "@/hooks/use-business-settings-form"

interface DeliveryTabProps {
  formData: BusinessSettingsFormData
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  handleSelectChange: (name: string, value: string) => void
  handleSwitchChange: (name: string, checked: boolean) => void
}

// Define delivery fee model options
const DELIVERY_FEE_MODELS = [
  { id: "fixed", name: "Fixed Fee" },
  { id: "distance", name: "Distance-Based" },
  { id: "mixed", name: "Combination (Fixed + Distance)" }
]

export const DeliveryTab = memo(function DeliveryTab({
  formData,
  handleChange,
  handleSelectChange,
  handleSwitchChange
}: DeliveryTabProps) {
  const isDeliveryComplete = formData.delivery_radius && formData.minimum_order_amount && formData.delivery_fee

  return (
    <div className="space-y-8">
      {/* Delivery Settings Section */}
      <Card className="border border-gray-200">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gray-800 rounded-lg p-2">
                <Truck className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Delivery Settings</CardTitle>
                <CardDescription className="text-gray-600 text-sm">
                  Configure your delivery options, fees, and service area
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {isDeliveryComplete ? (
                <div className="flex items-center space-x-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-xs font-medium">Complete</span>
                </div>
              ) : (
                <div className="flex items-center space-x-1 text-amber-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-xs font-medium">Incomplete</span>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {/* Loop Delivery Service */}
          <div className="mb-6 p-4 bg-emerald-50 border border-emerald-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Use Loop Delivery Service
                  </Label>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Enable this to use Loop's delivery network. When disabled, you'll handle deliveries yourself.</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <p className="text-xs text-gray-600">
                  Let Loop handle deliveries for you with our network of drivers
                </p>
              </div>
              <Switch
                checked={formData.use_loop_delivery}
                onCheckedChange={(checked) => handleSwitchChange('use_loop_delivery', checked)}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="delivery_radius" className="text-sm font-medium text-gray-700 flex items-center">
                  Delivery Radius (km)
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">Maximum distance you'll deliver from your location. Jersey is small, so 5-10km usually covers most areas.</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Input
                id="delivery_radius"
                name="delivery_radius"
                type="number"
                min="1"
                max="20"
                step="0.5"
                value={formData.delivery_radius}
                onChange={handleChange}
                className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="preparation_time_minutes" className="text-sm font-medium text-gray-700">
                  Preparation Time (minutes)
                </Label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">Average time needed to prepare orders. This helps set realistic delivery expectations.</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Input
                id="preparation_time_minutes"
                name="preparation_time_minutes"
                type="number"
                min="5"
                max="120"
                step="5"
                value={formData.preparation_time_minutes}
                onChange={handleChange}
                className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>
          </div>

          <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="minimum_order_amount" className="text-sm font-medium text-gray-700 flex items-center">
                  Minimum Order Amount (£)
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">Minimum order value required for delivery. Common values are £10-20.</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Input
                id="minimum_order_amount"
                name="minimum_order_amount"
                type="number"
                min="0"
                step="0.50"
                value={formData.minimum_order_amount}
                onChange={handleChange}
                className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="delivery_fee_model" className="text-sm font-medium text-gray-700">
                  Delivery Fee Model
                </Label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">Choose how delivery fees are calculated: fixed rate, distance-based, or combination.</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Select
                value={formData.delivery_fee_model}
                onValueChange={(value) => handleSelectChange('delivery_fee_model', value)}
              >
                <SelectTrigger className="border-gray-300 focus:border-emerald-500">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {DELIVERY_FEE_MODELS.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      {model.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="delivery_fee" className="text-sm font-medium text-gray-700 flex items-center">
                  {formData.delivery_fee_model === 'distance' ? 'Base Delivery Fee (£)' : 'Delivery Fee (£)'}
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">
                      {formData.delivery_fee_model === 'fixed' 
                        ? 'Fixed delivery fee charged for all orders.'
                        : formData.delivery_fee_model === 'distance'
                        ? 'Base fee plus distance charges.'
                        : 'Base fee for combination model.'
                      }
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Input
                id="delivery_fee"
                name="delivery_fee"
                type="number"
                min="0"
                step="0.25"
                value={formData.delivery_fee}
                onChange={handleChange}
                className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>

            {(formData.delivery_fee_model === 'distance' || formData.delivery_fee_model === 'mixed') && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Label htmlFor="delivery_fee_per_km" className="text-sm font-medium text-gray-700">
                    Fee per KM (£)
                  </Label>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Additional charge per kilometer for distance-based delivery fees.</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <Input
                  id="delivery_fee_per_km"
                  name="delivery_fee_per_km"
                  type="number"
                  min="0"
                  step="0.10"
                  value={formData.delivery_fee_per_km}
                  onChange={handleChange}
                  className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Delivery Methods Section */}
      <Card className="border border-gray-200">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-gray-800 rounded-lg p-2">
              <MapPin className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Delivery Methods</CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Configure which delivery methods you offer to customers
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {/* Pickup Options */}
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Pickup Available
                  </Label>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Allow customers to collect orders from your location</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <p className="text-xs text-gray-600">
                  Customers can collect orders directly from your business
                </p>
              </div>
              <Switch
                checked={formData.pickup_available}
                onCheckedChange={(checked) => handleSwitchChange('pickup_available', checked)}
              />
            </div>

            {/* Pickup Timing Options */}
            {formData.pickup_available && (
              <div className="ml-4 space-y-3 border-l-2 border-blue-200 pl-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium text-gray-700">ASAP Pickup</Label>
                    <p className="text-xs text-gray-600">Allow immediate pickup orders</p>
                  </div>
                  <Switch
                    checked={formData.pickup_asap_available}
                    onCheckedChange={(checked) => handleSwitchChange('pickup_asap_available', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium text-gray-700">Scheduled Time Pickup</Label>
                    <p className="text-xs text-gray-600">Allow customers to select specific pickup times</p>
                  </div>
                  <Switch
                    checked={formData.pickup_scheduled_time_available}
                    onCheckedChange={(checked) => handleSwitchChange('pickup_scheduled_time_available', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium text-gray-700">Scheduled Period Pickup</Label>
                    <p className="text-xs text-gray-600">Allow customers to select pickup time periods</p>
                  </div>
                  <Switch
                    checked={formData.pickup_scheduled_period_available}
                    onCheckedChange={(checked) => handleSwitchChange('pickup_scheduled_period_available', checked)}
                  />
                </div>
              </div>
            )}
          </div>

          <Separator className="my-6" />

          {/* Delivery Options */}
          <div className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium text-gray-700">ASAP Delivery</Label>
                  <p className="text-xs text-gray-600">Allow immediate delivery orders</p>
                </div>
                <Switch
                  checked={formData.delivery_asap_available}
                  onCheckedChange={(checked) => handleSwitchChange('delivery_asap_available', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Scheduled Time Delivery</Label>
                  <p className="text-xs text-gray-600">Allow customers to select specific delivery times</p>
                </div>
                <Switch
                  checked={formData.delivery_scheduled_time_available}
                  onCheckedChange={(checked) => handleSwitchChange('delivery_scheduled_time_available', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Scheduled Period Delivery</Label>
                  <p className="text-xs text-gray-600">Allow customers to select delivery time periods</p>
                </div>
                <Switch
                  checked={formData.delivery_scheduled_period_available}
                  onCheckedChange={(checked) => handleSwitchChange('delivery_scheduled_period_available', checked)}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Advance Booking Settings */}
      <Card className="border border-gray-200">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-gray-800 rounded-lg p-2">
              <Clock className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Advance Booking</CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Configure advance booking requirements for scheduled orders
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="min_advance_booking_minutes" className="text-sm font-medium text-gray-700">
                  Minimum Advance Booking (minutes)
                </Label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">Minimum time customers must book in advance for scheduled orders</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Input
                id="min_advance_booking_minutes"
                name="min_advance_booking_minutes"
                type="number"
                min="15"
                max="1440"
                step="15"
                value={formData.min_advance_booking_minutes}
                onChange={handleChange}
                className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="max_advance_booking_days" className="text-sm font-medium text-gray-700">
                  Maximum Advance Booking (days)
                </Label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">Maximum number of days in advance customers can book scheduled orders</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Input
                id="max_advance_booking_days"
                name="max_advance_booking_days"
                type="number"
                min="1"
                max="30"
                step="1"
                value={formData.max_advance_booking_days}
                onChange={handleChange}
                className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
})
