import { useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import type { OpeningHours } from "@/types/business"

export interface BusinessSettingsFormData {
  business_type_id: number | null;
  name: string;
  description: string;
  address: string;
  postcode: string;
  location: string;
  phone: string;
  delivery_radius: number;
  preparation_time_minutes: number;
  minimum_order_amount: number;
  delivery_fee: number;
  delivery_fee_model: string;
  delivery_fee_per_km: number;
  coordinates: string;
  logo_url: string;
  banner_url: string;
  hygiene_rating: string;
  allergens_info: string;
  attributes: string[];
  opening_hours: OpeningHours;
  use_loop_delivery: boolean;
  pickup_available: boolean;
  pickup_asap_available: boolean;
  pickup_scheduled_time_available: boolean;
  pickup_scheduled_period_available: boolean;
  delivery_asap_available: boolean;
  delivery_scheduled_time_available: boolean;
  delivery_scheduled_period_available: boolean;
  min_advance_booking_minutes: number;
  max_advance_booking_days: number;
}

const defaultFormData: BusinessSettingsFormData = {
  business_type_id: null,
  name: "",
  description: "",
  address: "",
  postcode: "",
  location: "",
  phone: "",
  delivery_radius: 5,
  preparation_time_minutes: 15,
  minimum_order_amount: 15.00,
  delivery_fee: 2.50,
  delivery_fee_model: "fixed",
  delivery_fee_per_km: 0.50,
  coordinates: "",
  logo_url: "",
  banner_url: "",
  hygiene_rating: "",
  allergens_info: "",
  attributes: [] as string[],
  opening_hours: {
    monday: { open: "09:00", close: "17:00" },
    tuesday: { open: "09:00", close: "17:00" },
    wednesday: { open: "09:00", close: "17:00" },
    thursday: { open: "09:00", close: "17:00" },
    friday: { open: "09:00", close: "17:00" },
    saturday: { open: "09:00", close: "17:00" },
    sunday: { open: "09:00", close: "17:00" }
  },
  use_loop_delivery: true,
  pickup_available: true,
  pickup_asap_available: true,
  pickup_scheduled_time_available: true,
  pickup_scheduled_period_available: false,
  delivery_asap_available: true,
  delivery_scheduled_time_available: true,
  delivery_scheduled_period_available: false,
  min_advance_booking_minutes: 30,
  max_advance_booking_days: 7
}

export function useBusinessSettingsForm(business: any) {
  const { toast } = useToast()
  const [formData, setFormData] = useState<BusinessSettingsFormData>(defaultFormData)
  const [saving, setSaving] = useState(false)

  // Update form data when business changes (only when business ID changes)
  useEffect(() => {
    if (business) {
      // Parse coordinates from PostgreSQL point format "(longitude,latitude)" to UI format "latitude,longitude"
      let coordinatesStr = ""
      if (business.coordinates) {
        if (typeof business.coordinates === 'string') {
          // Parse PostgreSQL point format: "(longitude,latitude)"
          const pointMatch = business.coordinates.match(/\(\s*([-\d.]+)\s*,\s*([-\d.]+)\s*\)/)
          if (pointMatch && pointMatch.length === 3) {
            const longitude = parseFloat(pointMatch[1])
            const latitude = parseFloat(pointMatch[2])
            if (!isNaN(longitude) && !isNaN(latitude)) {
              coordinatesStr = `${latitude},${longitude}` // UI format: latitude,longitude
            }
          }
        } else if (business.coordinates.lat && business.coordinates.lng) {
          // Handle object format (fallback)
          coordinatesStr = `${business.coordinates.lat},${business.coordinates.lng}`
        }
      }

      setFormData({
        business_type_id: business.business_type_id || null,
        name: business.name || "",
        description: business.description || "",
        address: business.address || "",
        postcode: business.postcode || "",
        location: business.location || "",
        phone: business.phone || "",
        delivery_radius: business.delivery_radius || 5,
        preparation_time_minutes: business.preparation_time_minutes || 15,
        minimum_order_amount: business.minimum_order_amount || 15.00,
        delivery_fee: business.delivery_fee || 2.50,
        delivery_fee_model: business.delivery_fee_model || "fixed",
        delivery_fee_per_km: business.delivery_fee_per_km || 0.50,
        coordinates: coordinatesStr,
        logo_url: business.logo_url || "",
        banner_url: business.banner_url || "",
        hygiene_rating: business.hygiene_rating || "",
        allergens_info: business.allergen_info || business.allergens_info || "",
        attributes: business.attributes || [],
        opening_hours: business.opening_hours || defaultFormData.opening_hours,
        use_loop_delivery: business.use_loop_delivery ?? true,
        pickup_available: business.pickup_available ?? true,
        pickup_asap_available: business.pickup_asap_available ?? true,
        pickup_scheduled_time_available: business.pickup_scheduled_time_available ?? true,
        pickup_scheduled_period_available: business.pickup_scheduled_period_available ?? false,
        delivery_asap_available: business.delivery_asap_available ?? true,
        delivery_scheduled_time_available: business.delivery_scheduled_time_available ?? true,
        delivery_scheduled_period_available: business.delivery_scheduled_period_available ?? false,
        min_advance_booking_minutes: business.min_advance_booking_minutes || 30,
        max_advance_booking_days: business.max_advance_booking_days || 7
      })
    }
  }, [business?.id]) // Only depend on business ID to prevent unnecessary re-renders

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }))
  }

  const handleSave = async (businessId: string | number) => {
    if (!businessId) {
      toast({
        variant: "destructive",
        title: "Error saving settings",
        description: "Business data not found. Please refresh the page and try again."
      })
      return false
    }

    setSaving(true)

    try {
      // Parse coordinates
      let coordinates = null
      if (formData.coordinates) {
        const [lat, lng] = formData.coordinates.split(',').map(parseFloat)
        if (!isNaN(lat) && !isNaN(lng)) {
          coordinates = `(${lng},${lat})`
        }
      }

      const dataToSave = {
        ...formData,
        coordinates
      }

      const token = localStorage.getItem('loop_jersey_auth_token') || ''
      const apiUrl = '/api/business-admin/settings-data'

      const response = await fetch(apiUrl, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        body: JSON.stringify(dataToSave)
      })

      const responseText = await response.text()
      let result: { business?: any, message?: string } = {}

      if (responseText) {
        result = JSON.parse(responseText)
      }

      if (!response.ok) {
        const errorData = result as { error?: string, details?: any }
        toast({
          variant: "destructive",
          title: "Error",
          description: errorData.error || `Failed to save settings (${response.status}). Please try again.`
        })
        return false
      }

      toast({
        title: "Settings Saved",
        description: "Your business settings have been updated successfully."
      })

      return result.business || true
    } catch (error) {
      console.error("Error saving business settings:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred. Please try again."
      })
      return false
    } finally {
      setSaving(false)
    }
  }

  return {
    formData,
    setFormData,
    saving,
    handleChange,
    handleSelectChange,
    handleSwitchChange,
    handleSave
  }
}
