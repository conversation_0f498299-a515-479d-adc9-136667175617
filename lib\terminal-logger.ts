/**
 * Utility for logging to the browser console and terminal
 * This helps with debugging authentication and access issues
 */

// Function to log to terminal (disabled for performance)
export async function logToTerminal(message: string, data?: any) {
  // Disabled to prevent ERR_CONNECTION_REFUSED errors and improve performance
  return;
}

// Function to log errors to terminal (disabled for performance)
export async function logErrorToTerminal(message: string, error?: any) {
  // Disabled to prevent ERR_CONNECTION_REFUSED errors and improve performance
  return;
}
