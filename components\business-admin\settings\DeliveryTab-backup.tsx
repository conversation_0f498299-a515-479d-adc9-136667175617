"use client"

import { memo } from "react"
import type { BusinessSettingsFormData } from "@/hooks/use-business-settings-form"

interface DeliveryTabProps {
  formData: BusinessSettingsFormData
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  handleSelectChange: (name: string, value: string) => void
  handleSwitchChange: (name: string, checked: boolean) => void
}

export const DeliveryTab = memo(function DeliveryTab({
  formData,
  handleChange,
  handleSelectChange,
  handleSwitchChange
}: DeliveryTabProps) {
  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Delivery Settings</h2>
      <p>Testing basic component structure...</p>
      <p>Form data available: {JSON.stringify(Object.keys(formData))}</p>
    </div>
  )
})
